#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP批处理工具
使用PyQt5界面和esptool.py进行批量烧录
"""

import sys
import os
import json
import threading
import time
from typing import List, Dict, Any
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QLabel, QPushButton, QCheckBox, QLineEdit, QSpinBox, QProgressBar,
    QTextEdit, QFileDialog, QGroupBox, QGridLayout, QListWidget,
    QListWidgetItem, QMessageBox, QComboBox, QSplitter
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon

import serial.tools.list_ports
import esptool


class PortScanner(QThread):
    """端口扫描线程"""
    ports_found = pyqtSignal(list)

    def run(self):
        """扫描可用串口"""
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append({
                'device': port.device,
                'description': port.description,
                'hwid': port.hwid
            })
        self.ports_found.emit(ports)


class AutoBootloaderWorker(QThread):
    """自动进入Bootloader模式的工作线程"""
    log_updated = pyqtSignal(str)  # 日志消息
    device_processed = pyqtSignal(str, bool)  # 设备, 是否成功

    def __init__(self, port: str, baud_rate: int = 115200):
        super().__init__()
        self.port = port
        self.baud_rate = baud_rate
        self.is_cancelled = False

    def cancel(self):
        """取消操作"""
        self.is_cancelled = True

    def run(self):
        """尝试让设备进入bootloader模式"""
        try:
            self.log_updated.emit(f"检测到新设备: {self.port}")
            self.log_updated.emit(f"正在尝试让设备进入bootloader模式...")

            # 使用esptool的detect_chip尝试连接设备
            from esptool.cmds import detect_chip

            # 尝试多种连接模式
            connect_modes = ['default-reset', 'no-reset', 'usb-reset']

            for connect_mode in connect_modes:
                if self.is_cancelled:
                    break

                try:
                    self.log_updated.emit(f"尝试连接模式: {connect_mode}")

                    with detect_chip(self.port, baud=self.baud_rate, connect_mode=connect_mode) as esp:
                        chip_desc = esp.get_chip_description()
                        self.log_updated.emit(f"成功连接到 {chip_desc}")
                        self.log_updated.emit(f"设备 {self.port} 已进入bootloader模式")
                        self.device_processed.emit(self.port, True)
                        return

                except Exception as e:
                    self.log_updated.emit(f"连接模式 {connect_mode} 失败: {str(e)}")
                    continue

            # 如果所有模式都失败，记录失败信息
            self.log_updated.emit(f"无法让设备 {self.port} 进入bootloader模式")
            self.device_processed.emit(self.port, False)

        except Exception as e:
            self.log_updated.emit(f"处理设备 {self.port} 时发生错误: {str(e)}")
            self.device_processed.emit(self.port, False)


class ReadFlashWorker(QThread):
    """固件读取工作线程"""
    progress_updated = pyqtSignal(str, int)  # port, progress
    log_updated = pyqtSignal(str, str)  # port, message
    finished = pyqtSignal(str, bool, str)  # port, success, error_message

    def __init__(self, port: str, start_address: int, size: int, output_file: str, baud_rate: int, chip_type: str = "auto", connect_mode: str = "default-reset", no_stub: bool = False):
        super().__init__()
        self.port = port
        self.start_address = start_address
        self.size = size
        self.output_file = output_file
        self.baud_rate = baud_rate
        self.chip_type = chip_type
        self.connect_mode = connect_mode
        self.no_stub = no_stub
        self.is_cancelled = False

    def cancel(self):
        """取消读取"""
        self.is_cancelled = True

    def run(self):
        """执行固件读取 - 使用高级API"""
        try:
            self.log_updated.emit(self.port, f"开始读取端口: {self.port}")
            self.log_updated.emit(self.port, f"读取地址: 0x{self.start_address:X}")
            self.log_updated.emit(self.port, f"读取大小: {self.size} 字节")
            self.log_updated.emit(self.port, f"输出文件: {self.output_file}")

            # 使用esptool高级API进行读取
            self.progress_updated.emit(self.port, 10)
            self._read_with_high_level_api()

        except Exception as e:
            error_msg = self.parse_error_message(str(e))
            self.log_updated.emit(self.port, f"读取失败: {error_msg}")
            self.finished.emit(self.port, False, error_msg)

    def _read_with_high_level_api(self):
        """使用esptool高级API进行读取 - 基于官方文档示例"""
        try:
            # 导入高级API函数
            from esptool.cmds import detect_chip, attach_flash, flash_id, read_flash, reset_chip, run_stub

            self.log_updated.emit(self.port, f"复位模式: {self.connect_mode}")
            self.progress_updated.emit(self.port, 20)

            # 使用detect_chip连接设备 - 官方推荐方式
            with detect_chip(self.port, baud=self.baud_rate, connect_mode=self.connect_mode) as esp:
                self.log_updated.emit(self.port, f"已连接到 {esp.get_chip_description()}")
                self.progress_updated.emit(self.port, 30)

                # 根据no_stub设置决定是否运行stub loader
                if not self.no_stub:
                    try:
                        esp = run_stub(esp)
                        self.log_updated.emit(self.port, "已加载高速读取stub")
                    except Exception as e:
                        self.log_updated.emit(self.port, f"stub加载失败，使用ROM loader: {str(e)}")
                else:
                    self.log_updated.emit(self.port, "已禁用Stub Loader，使用ROM loader模式")

                self.progress_updated.emit(self.port, 40)

                # 附加Flash - 官方文档要求的步骤
                attach_flash(esp)
                self.log_updated.emit(self.port, "Flash附加完成")

                # 显示Flash信息
                flash_id(esp)
                self.log_updated.emit(self.port, "Flash检测完成")
                self.progress_updated.emit(self.port, 50)

                if self.is_cancelled:
                    self.log_updated.emit(self.port, "读取已取消")
                    self.finished.emit(self.port, False, "用户取消操作")
                    return

                # 使用高级API读取Flash - 官方推荐方式
                self.log_updated.emit(self.port, "开始读取Flash...")
                self.progress_updated.emit(self.port, 60)

                read_flash(
                    esp,
                    self.start_address,
                    self.size,
                    output=self.output_file,
                    flash_size='keep',
                    no_progress=True  # 禁用内置进度显示，使用我们自己的
                )

                self.progress_updated.emit(self.port, 90)
                self.log_updated.emit(self.port, "Flash读取完成")

                # 重启设备 - 官方推荐方式
                self.log_updated.emit(self.port, "正在重启设备...")
                reset_chip(esp, "hard-reset")

                self.progress_updated.emit(self.port, 100)
                self.log_updated.emit(self.port, f"读取完成，文件已保存到: {self.output_file}")
                self.finished.emit(self.port, True, "读取成功")

        except Exception as e:
            error_msg = self.parse_error_message(str(e))
            self.log_updated.emit(self.port, f"高级API读取失败: {error_msg}")
            self.finished.emit(self.port, False, error_msg)

    def parse_error_message(self, error_str: str) -> str:
        """解析错误信息，提供更友好的错误描述"""
        error_lower = error_str.lower()

        if "permission denied" in error_lower or "access is denied" in error_lower:
            return "端口被占用或权限不足，请检查是否有其他程序正在使用该端口"
        elif "no such file or directory" in error_lower:
            return "串口设备不存在，请检查设备连接"
        elif "timeout" in error_lower or "timed out" in error_lower:
            return "连接超时，请检查设备连接和波特率设置"
        elif "invalid head of packet" in error_lower:
            return "通信协议错误，请尝试重新连接设备"
        elif "chip not in download mode" in error_lower:
            return "芯片未进入下载模式，请按住BOOT键并重启设备"
        elif "failed to connect" in error_lower:
            return "连接失败，请检查设备连接和端口设置"
        elif "failed to enter flash download mode" in error_lower and ("01 06" in error_str or "0x06" in error_str):
            return "ESP32-S3兼容性问题：建议启用'ESP32-S3兼容模式'或'禁用Stub Loader'选项"
        elif "stub flasher json file" in error_lower and "not found" in error_lower:
            return "Stub Loader文件缺失：建议启用'ESP32-S3兼容模式'或'禁用Stub Loader'选项"
        elif "operation or feature not supported" in error_lower:
            return "操作不支持：建议启用'ESP32-S3兼容模式'或降低波特率到57600/9600"
        elif "read_flash" in error_lower and "failed" in error_lower:
            return "高级API读取失败：建议启用'ESP32-S3兼容模式'或检查读取地址是否正确"
        elif "address" in error_lower and ("invalid" in error_lower or "out of range" in error_lower):
            return "读取地址无效：请检查地址范围是否正确"
        elif "flash read err" in error_lower:
            return "Flash读取错误，可能是硬件问题"
        elif "connect_attempts" in error_lower or "connection failed" in error_lower:
            return "连接尝试失败：建议启用'ESP32-S3兼容模式'并使用手动复位"
        else:
            return error_str


class FlashWorker(QThread):
    """固件烧录工作线程"""
    progress_updated = pyqtSignal(str, int)  # port, progress
    log_updated = pyqtSignal(str, str)  # port, message
    finished = pyqtSignal(str, bool, str)  # port, success, error_message

    def __init__(self, port: str, firmware_files: List[Dict], baud_rate: int, chip_type: str = "auto", manual_reset: bool = False, connect_mode: str = "default-reset", no_stub: bool = False):
        super().__init__()
        self.port = port
        self.firmware_files = firmware_files
        self.baud_rate = baud_rate
        self.chip_type = chip_type
        self.manual_reset = manual_reset
        self.connect_mode = connect_mode
        self.no_stub = no_stub
        self.is_cancelled = False

    def validate_address(self, address: str) -> bool:
        """验证地址格式是否正确"""
        try:
            if address.startswith('0x') or address.startswith('0X'):
                int(address, 16)
                return True
            else:
                # 尝试解析为十进制
                int(address)
                return True
        except ValueError:
            return False
    
    def cancel(self):
        """取消烧录"""
        self.is_cancelled = True


    
    def run(self):
        """执行固件烧录 - 完全使用高级API"""
        try:
            self.log_updated.emit(self.port, f"开始烧录端口: {self.port}")

            # 验证所有地址格式
            for fw_file in self.firmware_files:
                if not self.validate_address(fw_file['address']):
                    error_msg = f"无效的烧录地址格式: {fw_file['address']}"
                    self.log_updated.emit(self.port, error_msg)
                    self.finished.emit(self.port, False, error_msg)
                    return

            # 检查固件文件是否存在
            for fw_file in self.firmware_files:
                if not os.path.exists(fw_file['path']):
                    error_msg = f"固件文件不存在: {fw_file['path']}"
                    self.log_updated.emit(self.port, error_msg)
                    self.finished.emit(self.port, False, error_msg)
                    return

            # 使用esptool高级API进行烧录
            self.progress_updated.emit(self.port, 10)
            self._flash_with_high_level_api()

        except Exception as e:
            error_msg = self.parse_error_message(str(e))
            self.log_updated.emit(self.port, f"烧录失败: {error_msg}")
            self.finished.emit(self.port, False, error_msg)

    def _flash_with_high_level_api(self):
        """使用esptool高级API进行烧录 - 基于官方文档示例"""
        try:
            # 导入高级API函数
            from esptool.cmds import detect_chip, attach_flash, flash_id, write_flash, reset_chip, run_stub

            self.log_updated.emit(self.port, f"复位模式: {self.connect_mode}")
            self.progress_updated.emit(self.port, 20)

            # 使用detect_chip连接设备 - 官方推荐方式
            with detect_chip(self.port, baud=self.baud_rate, connect_mode=self.connect_mode) as esp:
                self.log_updated.emit(self.port, f"已连接到 {esp.get_chip_description()}")
                self.progress_updated.emit(self.port, 30)

                # 根据no_stub设置决定是否运行stub loader
                if not self.no_stub:
                    try:
                        esp = run_stub(esp)
                        self.log_updated.emit(self.port, "已加载高速烧录stub")
                    except Exception as e:
                        self.log_updated.emit(self.port, f"stub加载失败，使用ROM loader: {str(e)}")
                else:
                    self.log_updated.emit(self.port, "已禁用Stub Loader，使用ROM loader模式")

                self.progress_updated.emit(self.port, 40)

                # 附加Flash - 官方文档要求的步骤
                attach_flash(esp)
                self.log_updated.emit(self.port, "Flash附加完成")

                # 显示Flash信息
                flash_id(esp)
                self.log_updated.emit(self.port, "Flash检测完成")
                self.progress_updated.emit(self.port, 50)

                if self.is_cancelled:
                    self.log_updated.emit(self.port, "烧录已取消")
                    self.finished.emit(self.port, False, "用户取消操作")
                    return

                # 准备地址和数据对 - 按照官方文档格式
                addr_data = []
                for fw_file in self.firmware_files:
                    address = int(fw_file['address'], 16) if fw_file['address'].startswith('0x') else int(fw_file['address'])
                    addr_data.append((address, fw_file['path']))
                    self.log_updated.emit(self.port, f"准备烧录: {fw_file['name']} -> 0x{address:X}")

                self.progress_updated.emit(self.port, 60)

                # 使用高级API烧录 - 官方推荐方式
                self.log_updated.emit(self.port, "开始烧录固件...")
                write_flash(
                    esp,
                    addr_data,
                    compress=True,           # 启用压缩以提高速度
                    no_progress=True,        # 禁用内置进度显示，使用我们自己的
                    flash_freq='keep',       # 保持Flash频率
                    flash_mode='keep',       # 保持Flash模式
                    flash_size='keep'        # 保持Flash大小
                )

                self.progress_updated.emit(self.port, 90)
                self.log_updated.emit(self.port, "固件烧录完成")

                # 重启设备 - 官方推荐方式
                self.log_updated.emit(self.port, "正在重启设备...")
                reset_chip(esp, "hard-reset")

                self.progress_updated.emit(self.port, 100)
                self.log_updated.emit(self.port, "烧录完成，设备已重启")
                self.finished.emit(self.port, True, "烧录成功")

        except Exception as e:
            error_msg = self.parse_error_message(str(e))
            self.log_updated.emit(self.port, f"高级API烧录失败: {error_msg}")
            self.finished.emit(self.port, False, error_msg)




    def parse_error_message(self, error_str: str) -> str:
        """解析错误信息，提供更友好的错误描述"""
        error_lower = error_str.lower()

        if "permission denied" in error_lower or "access is denied" in error_lower:
            return "端口被占用或权限不足，请检查是否有其他程序正在使用该端口"
        elif "no such file or directory" in error_lower:
            return "串口设备不存在，请检查设备连接"
        elif "timeout" in error_lower or "timed out" in error_lower:
            return "连接超时，请检查设备连接和波特率设置"
        elif "invalid head of packet" in error_lower:
            return "通信协议错误，请尝试重新连接设备"
        elif "chip not in download mode" in error_lower:
            return "芯片未进入下载模式，请按住BOOT键并重启设备"
        elif "failed to connect" in error_lower:
            return "连接失败，请检查设备连接和端口设置"
        elif "failed to enter flash download mode" in error_lower and ("01 06" in error_str or "0x06" in error_str):
            return "ESP32-S3兼容性问题：建议启用'ESP32-S3兼容模式'或'禁用Stub Loader'选项"
        elif "stub flasher json file" in error_lower and "not found" in error_lower:
            return "Stub Loader文件缺失：建议启用'ESP32-S3兼容模式'或'禁用Stub Loader'选项"
        elif "operation or feature not supported" in error_lower:
            return "操作不支持：建议启用'ESP32-S3兼容模式'或降低波特率到57600/9600"
        elif "write_flash" in error_lower and "failed" in error_lower:
            return "高级API烧录失败：建议启用'ESP32-S3兼容模式'或检查烧录地址是否正确"
        elif "address" in error_lower and ("invalid" in error_lower or "out of range" in error_lower):
            return "烧录地址无效：ESP32-S3建议使用0x10000作为应用程序地址"
        elif "flash read err" in error_lower:
            return "Flash读取错误，可能是硬件问题"
        elif "flash write err" in error_lower:
            return "Flash写入错误，可能是固件文件损坏或硬件问题"
        elif "connect_attempts" in error_lower or "connection failed" in error_lower:
            return "连接尝试失败：建议启用'ESP32-S3兼容模式'并使用手动复位"
        else:
            return error_str


class ESP32FlasherMainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_file = "config.json"
        self.port_workers = {}  # 存储每个端口的工作线程
        self.read_workers = {}  # 存储读取工作线程
        self.auto_bootloader_workers = {}  # 存储自动bootloader工作线程
        self.firmware_files = []  # 固件文件列表
        self.port_status = {}  # 存储每个端口的状态
        self.total_ports = 0  # 总端口数
        self.completed_ports = 0  # 已完成端口数

        # 自动监控相关
        self.auto_monitor_enabled = False
        self.known_ports = set()  # 已知端口列表
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_new_devices)

        self.init_ui()
        self.load_config()
        self.scan_ports()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("ESP批处理工具")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 底部按钮
        button_layout = QHBoxLayout()

        self.scan_button = QPushButton("扫描端口")
        self.scan_button.clicked.connect(self.scan_ports)
        button_layout.addWidget(self.scan_button)

        # 自动监控按钮
        self.auto_monitor_button = QPushButton("启用自动监控")
        self.auto_monitor_button.clicked.connect(self.toggle_auto_monitor)
        self.auto_monitor_button.setToolTip(
            "启用后将自动监控新插入的ESP32设备\n"
            "检测到新设备时自动尝试让其进入bootloader模式\n"
            "解决裸板不断重连的问题"
        )
        button_layout.addWidget(self.auto_monitor_button)

        self.start_button = QPushButton("开始烧录")
        self.start_button.clicked.connect(self.start_flashing)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("停止烧录")
        self.stop_button.clicked.connect(self.stop_flashing)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        button_layout.addStretch()
        main_layout.addLayout(button_layout)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 端口选择组
        port_group = QGroupBox("端口选择")
        port_layout = QVBoxLayout(port_group)
        
        self.port_list = QListWidget()
        port_layout.addWidget(self.port_list)
        
        layout.addWidget(port_group)
        
        # 固件文件组
        firmware_group = QGroupBox("固件文件")
        firmware_layout = QVBoxLayout(firmware_group)
        
        fw_button_layout = QHBoxLayout()
        self.add_firmware_button = QPushButton("添加固件")
        self.add_firmware_button.clicked.connect(self.add_firmware_file)
        fw_button_layout.addWidget(self.add_firmware_button)

        self.edit_address_button = QPushButton("编辑地址")
        self.edit_address_button.clicked.connect(self.edit_firmware_address)
        fw_button_layout.addWidget(self.edit_address_button)

        self.remove_firmware_button = QPushButton("移除固件")
        self.remove_firmware_button.clicked.connect(self.remove_firmware_file)
        fw_button_layout.addWidget(self.remove_firmware_button)

        firmware_layout.addLayout(fw_button_layout)
        
        self.firmware_list = QListWidget()
        firmware_layout.addWidget(self.firmware_list)
        
        layout.addWidget(firmware_group)
        
        # 参数设置组
        params_group = QGroupBox("烧录参数")
        params_layout = QGridLayout(params_group)

        params_layout.addWidget(QLabel("波特率:"), 0, 0)
        self.baud_rate_combo = QComboBox()
        self.baud_rate_combo.addItems(['9600', '57600', '115200', '460800', '921600', '1500000'])
        self.baud_rate_combo.setCurrentText('57600')  # 对ESP32-S3使用更稳定的默认值
        self.baud_rate_combo.setToolTip("ESP32-S3建议使用57600或更低波特率以提高稳定性")
        params_layout.addWidget(self.baud_rate_combo, 0, 1)

        params_layout.addWidget(QLabel("芯片类型:"), 1, 0)
        self.chip_type_combo = QComboBox()
        self.chip_type_combo.addItems([
            'auto (自动检测)',
            'esp32',
            'esp32s2',
            'esp32s3',
            'esp32c3',
            'esp32c6',
            'esp32h2'
        ])
        self.chip_type_combo.setCurrentText('auto (自动检测)')
        params_layout.addWidget(self.chip_type_combo, 1, 1)

        params_layout.addWidget(QLabel("默认起始地址:"), 2, 0)
        self.default_address_combo = QComboBox()
        self.default_address_combo.setEditable(True)
        self.default_address_combo.addItems([
            '0x1000',    # Bootloader
            '0x8000',    # Partition table
            '0xe000',    # Boot app0
            '0x10000',   # Application firmware
            '0x3d0000'   # SPIFFS/LittleFS
        ])
        self.default_address_combo.setCurrentText('0x10000')
        params_layout.addWidget(self.default_address_combo, 2, 1)

        # 添加手动复位选项
        params_layout.addWidget(QLabel("复位模式:"), 3, 0)
        self.reset_mode_combo = QComboBox()
        self.reset_mode_combo.addItems([
            '自动复位 (--before default-reset)',
            '手动复位 (--before no-reset)',
            'USB复位 (--before usb-reset)'
        ])
        self.reset_mode_combo.setCurrentText('手动复位 (--before no-reset)')
        self.reset_mode_combo.setToolTip(
            "手动复位: 需要手动按BOOT+RESET进入下载模式，兼容性最好\n"
            "自动复位: esptool自动控制复位，可能在某些硬件上失败\n"
            "USB复位: 适用于支持USB复位的开发板"
        )
        params_layout.addWidget(self.reset_mode_combo, 3, 1)

        # 添加禁用Stub Loader选项
        self.no_stub_checkbox = QCheckBox("禁用Stub Loader (--no-stub)")
        self.no_stub_checkbox.setToolTip(
            "禁用Stub Loader，强制使用ROM loader\n"
            "解决ESP32-S3兼容性问题和'Failed to enter flash download mode'错误\n"
            "启用此选项会降低烧录速度，但提高兼容性"
        )
        params_layout.addWidget(self.no_stub_checkbox, 4, 0, 1, 2)

        # 添加ESP32-S3兼容模式选项
        self.esp32s3_compat_checkbox = QCheckBox("ESP32-S3兼容模式")
        self.esp32s3_compat_checkbox.setToolTip(
            "启用ESP32-S3专用兼容模式\n"
            "- 自动降低波特率到57600\n"
            "- 使用较小的数据块大小\n"
            "- 启用保守的烧录参数\n"
            "- 自动启用--no-stub选项"
        )
        self.esp32s3_compat_checkbox.stateChanged.connect(self.on_esp32s3_compat_changed)
        params_layout.addWidget(self.esp32s3_compat_checkbox, 5, 0, 1, 2)

        layout.addWidget(params_group)

        # 读取固件组
        read_group = QGroupBox("读取固件")
        read_layout = QGridLayout(read_group)

        read_layout.addWidget(QLabel("起始地址:"), 0, 0)
        self.read_address_combo = QComboBox()
        self.read_address_combo.setEditable(True)
        self.read_address_combo.addItems([
            '0x0 (0B)',        # 从头开始读取
            '0x1000 (4KB)',    # Bootloader
            '0x8000 (32KB)',   # Partition table
            '0x10000 (64KB)',  # Application firmware
            '0x100000 (1MB)',  # 1MB位置
            '0x200000 (2MB)'   # 2MB位置
        ])
        self.read_address_combo.setCurrentText('0x0')
        read_layout.addWidget(self.read_address_combo, 0, 1)

        read_layout.addWidget(QLabel("读取大小:"), 1, 0)
        self.read_size_combo = QComboBox()
        self.read_size_combo.setEditable(True)
        self.read_size_combo.addItems([
            '0x1000 (4KB)',      # 4KB
            '0x10000 (64KB)',    # 64KB
            '0x100000 (1MB)',    # 1MB
            '0x200000 (2MB)',    # 2MB
            '0x400000 (4MB)',    # 4MB
            '0x800000 (8MB)',    # 8MB
            '0xF00000 (15MB)',   # 15MB
            '0x1000000 (16MB)',  # 16MB
            '0x2000000 (32MB)'   # 32MB
        ])
        self.read_size_combo.setCurrentText('0x400000')  # 默认4MB
        self.read_size_combo.setToolTip("读取的字节数，常见Flash大小：4MB、8MB、15MB、16MB、32MB等")
        read_layout.addWidget(self.read_size_combo, 1, 1)

        # 读取按钮
        read_button_layout = QHBoxLayout()
        self.read_flash_button = QPushButton("读取Flash")
        self.read_flash_button.clicked.connect(self.start_reading_flash)
        self.read_flash_button.setToolTip("从选中的端口读取Flash内容并保存为bin文件")
        read_button_layout.addWidget(self.read_flash_button)

        self.stop_read_button = QPushButton("停止读取")
        self.stop_read_button.clicked.connect(self.stop_reading_flash)
        self.stop_read_button.setEnabled(False)
        read_button_layout.addWidget(self.stop_read_button)

        read_layout.addLayout(read_button_layout, 2, 0, 1, 2)

        layout.addWidget(read_group)

        layout.addStretch()
        return panel

    def on_esp32s3_compat_changed(self, state):
        """ESP32-S3兼容模式状态改变处理"""
        if state == 2:  # 选中
            # 自动配置ESP32-S3兼容参数
            self.baud_rate_combo.setCurrentText('57600')
            self.chip_type_combo.setCurrentText('esp32s3')
            self.reset_mode_combo.setCurrentText('手动复位 (--before no-reset)')
            self.no_stub_checkbox.setChecked(True)
            self.log_message("已启用ESP32-S3兼容模式：波特率57600，手动复位，禁用Stub Loader")
        else:  # 取消选中
            self.log_message("已禁用ESP32-S3兼容模式")
    
    def create_right_panel(self) -> QWidget:
        """创建右侧日志和进度面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 进度显示组
        progress_group = QGroupBox("烧录进度")
        progress_layout = QVBoxLayout(progress_group)

        # 总体进度条
        total_progress_widget = QWidget()
        total_progress_layout = QHBoxLayout(total_progress_widget)
        total_progress_layout.addWidget(QLabel("总体进度:"))
        self.total_progress_bar = QProgressBar()
        self.total_progress_bar.setMinimumWidth(200)
        total_progress_layout.addWidget(self.total_progress_bar)
        self.total_progress_label = QLabel("0/0")
        total_progress_layout.addWidget(self.total_progress_label)
        progress_layout.addWidget(total_progress_widget)

        # 各端口进度
        self.progress_widget = QWidget()
        self.progress_layout = QVBoxLayout(self.progress_widget)
        progress_layout.addWidget(self.progress_widget)

        layout.addWidget(progress_group)
        
        # 日志显示组
        log_group = QGroupBox("烧录日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return panel

    def scan_ports(self):
        """扫描可用端口"""
        self.scan_button.setEnabled(False)
        self.scan_button.setText("扫描中...")

        self.port_scanner = PortScanner()
        self.port_scanner.ports_found.connect(self.on_ports_found)
        self.port_scanner.start()

    def on_ports_found(self, ports):
        """端口扫描完成回调"""
        self.port_list.clear()

        for port_info in ports:
            item = QListWidgetItem()
            checkbox = QCheckBox(f"{port_info['device']} - {port_info['description']}")
            checkbox.setProperty('port_device', port_info['device'])

            self.port_list.addItem(item)
            self.port_list.setItemWidget(item, checkbox)

        self.scan_button.setEnabled(True)
        self.scan_button.setText("扫描端口")
        self.log_message(f"扫描完成，发现 {len(ports)} 个端口")

    def add_firmware_file(self):
        """添加固件文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择固件文件", "", "固件文件 (*.bin *.hex);;所有文件 (*.*)"
        )

        if files:
            # 获取默认地址
            default_address = self.default_address_combo.currentText()

            for file_path in files:
                # 使用默认地址，但允许用户修改
                address, ok = self.get_flash_address(default_address)
                if ok:
                    fw_info = {
                        'path': file_path,
                        'address': address,
                        'name': os.path.basename(file_path)
                    }
                    self.firmware_files.append(fw_info)

                    item_text = f"{fw_info['name']} @ {fw_info['address']}"
                    self.firmware_list.addItem(item_text)

                    # 自动递增地址（如果是十六进制）
                    if address.startswith('0x'):
                        try:
                            addr_int = int(address, 16) + 0x1000  # 递增4KB
                            next_address = f"0x{addr_int:x}"
                            self.default_address_combo.setCurrentText(next_address)
                        except ValueError:
                            pass

    def get_flash_address(self, default_address="0x1000"):
        """获取烧录地址对话框"""
        from PyQt5.QtWidgets import QInputDialog

        while True:
            address, ok = QInputDialog.getText(
                self, "设置烧录地址", "请输入烧录地址 (如: 0x1000):", text=default_address
            )

            if not ok:
                return address, ok

            # 验证地址格式
            if self.validate_address_format(address):
                return address, ok
            else:
                QMessageBox.warning(
                    self, "地址格式错误",
                    "请输入有效的十六进制地址格式 (如: 0x1000) 或十进制数字"
                )

    def validate_address_format(self, address: str) -> bool:
        """验证地址格式是否正确"""
        try:
            if address.startswith('0x') or address.startswith('0X'):
                int(address, 16)
                return True
            else:
                # 尝试解析为十进制
                int(address)
                return True
        except ValueError:
            return False

    def edit_firmware_address(self):
        """编辑选中固件文件的地址"""
        current_row = self.firmware_list.currentRow()
        if current_row >= 0:
            fw_info = self.firmware_files[current_row]
            current_address = fw_info['address']

            new_address, ok = self.get_flash_address(current_address)
            if ok and new_address != current_address:
                # 更新固件信息
                fw_info['address'] = new_address

                # 更新列表显示
                item_text = f"{fw_info['name']} @ {fw_info['address']}"
                self.firmware_list.item(current_row).setText(item_text)
        else:
            QMessageBox.information(self, "提示", "请先选择要编辑的固件文件")

    def remove_firmware_file(self):
        """移除选中的固件文件"""
        current_row = self.firmware_list.currentRow()
        if current_row >= 0:
            self.firmware_list.takeItem(current_row)
            del self.firmware_files[current_row]

    def get_selected_ports(self) -> List[str]:
        """获取选中的端口列表"""
        selected_ports = []
        for i in range(self.port_list.count()):
            item = self.port_list.item(i)
            checkbox = self.port_list.itemWidget(item)
            if checkbox.isChecked():
                port_device = checkbox.property('port_device')
                selected_ports.append(port_device)
        return selected_ports

    def start_flashing(self):
        """开始烧录"""
        selected_ports = self.get_selected_ports()

        if not selected_ports:
            QMessageBox.warning(self, "警告", "请至少选择一个端口")
            return

        if not self.firmware_files:
            QMessageBox.warning(self, "警告", "请至少添加一个固件文件")
            return

        # 验证所有固件文件的地址格式
        for fw_file in self.firmware_files:
            if not self.validate_address_format(fw_file['address']):
                QMessageBox.warning(
                    self, "地址格式错误",
                    f"固件文件 '{fw_file['name']}' 的地址格式无效: {fw_file['address']}\n"
                    "请编辑地址为有效的十六进制格式 (如: 0x1000)"
                )
                return

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 初始化状态
        self.total_ports = len(selected_ports)
        self.completed_ports = 0
        self.port_status = {}
        for port in selected_ports:
            self.port_status[port] = {"status": "进行中", "progress": 0}

        # 更新总体进度
        self.update_total_progress()

        # 清空之前的进度条
        self.clear_progress_bars()

        # 为每个选中的端口创建进度条
        self.create_progress_bars(selected_ports)

        # 启动烧录线程
        baud_rate = int(self.baud_rate_combo.currentText())

        # 获取芯片类型设置
        chip_type_text = self.chip_type_combo.currentText()
        if chip_type_text.startswith('auto'):
            chip_type = "auto"
        else:
            chip_type = chip_type_text

        # 获取复位模式设置
        reset_mode_text = self.reset_mode_combo.currentText()
        manual_reset = reset_mode_text.startswith('手动复位')

        # 确定connect_mode参数
        if reset_mode_text.startswith('手动复位'):
            connect_mode_for_ui = 'no-reset'
        elif reset_mode_text.startswith('USB复位'):
            connect_mode_for_ui = 'usb-reset'
        else:  # 自动复位
            connect_mode_for_ui = 'default-reset'

        # 获取no_stub设置
        no_stub = self.no_stub_checkbox.isChecked()

        for port in selected_ports:
            # 如果是手动复位模式，为每个端口单独显示提示
            if manual_reset:
                # 在日志中显示手动复位步骤
                self.log_message(f"\n=== 手动复位模式 - {port} ===")
                self.log_message("请按照以下步骤操作：")
                self.log_message("1. 按住ESP32上的BOOT键（或IO0键）")
                self.log_message("2. 按一下RESET键（或EN键）")
                self.log_message("3. 松开RESET键，继续按住BOOT键")
                self.log_message("4. 点击下方对话框的'是'开始烧录")
                self.log_message("5. 烧录开始后可以松开BOOT键")
                self.log_message("=" * 40)

                reply = QMessageBox.question(
                    self, f"手动复位模式 - {port}",
                    f"准备烧录端口: {port}\n\n"
                    "请确认已按照日志区域显示的步骤完成手动复位操作\n\n"
                    "ESP32应该已经进入下载模式\n\n"
                    "是否继续烧录此端口？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply != QMessageBox.Yes:
                    self.log_message(f"跳过端口: {port}")
                    continue

            worker = FlashWorker(port, self.firmware_files, baud_rate, chip_type, manual_reset, connect_mode_for_ui, no_stub)
            worker.progress_updated.connect(self.update_progress)
            worker.log_updated.connect(self.log_message_with_port)
            worker.finished.connect(self.on_flash_finished)

            self.port_workers[port] = worker
            worker.start()

            # 在手动复位模式下，为每个端口添加延迟，避免同时操作多个设备
            if manual_reset and len(selected_ports) > 1:
                import time
                time.sleep(1)

        self.log_message("开始批量烧录...")

    def stop_flashing(self):
        """停止烧录"""
        for worker in self.port_workers.values():
            worker.cancel()

        self.log_message("正在停止烧录...")

    def clear_progress_bars(self):
        """清空进度条"""
        for i in reversed(range(self.progress_layout.count())):
            child = self.progress_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

    def create_progress_bars(self, ports: List[str]):
        """为选中的端口创建进度条"""
        for port in ports:
            port_widget = QWidget()
            port_layout = QHBoxLayout(port_widget)

            label = QLabel(f"{port}:")
            label.setMinimumWidth(80)
            port_layout.addWidget(label)

            progress_bar = QProgressBar()
            progress_bar.setProperty('port', port)
            progress_bar.setMinimumWidth(200)
            port_layout.addWidget(progress_bar)

            status_label = QLabel("准备中...")
            status_label.setProperty('port', port)
            status_label.setMinimumWidth(100)
            port_layout.addWidget(status_label)

            self.progress_layout.addWidget(port_widget)

    def update_progress(self, port: str, progress: int):
        """更新指定端口的进度"""
        # 更新端口状态
        if port in self.port_status:
            self.port_status[port]["progress"] = progress

        # 更新进度条
        for i in range(self.progress_layout.count()):
            widget = self.progress_layout.itemAt(i).widget()
            if widget:
                progress_bar = widget.findChild(QProgressBar)
                if progress_bar and progress_bar.property('port') == port:
                    progress_bar.setValue(progress)

                    # 更新状态标签
                    status_label = None
                    for child in widget.children():
                        if isinstance(child, QLabel) and child.property('port') == port:
                            status_label = child
                            break

                    if status_label:
                        if progress < 100:
                            status_label.setText(f"烧录中... {progress}%")
                        else:
                            status_label.setText("烧录完成")
                    break

    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def log_message_with_port(self, port: str, message: str):
        """添加带端口信息的日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] [{port}] {message}")

    def on_flash_finished(self, port: str, success: bool, error_message: str = ""):
        """烧录完成回调"""
        if port in self.port_workers:
            del self.port_workers[port]

        # 更新端口状态
        if port in self.port_status:
            self.port_status[port]["status"] = "成功" if success else "失败"
            if not success and error_message:
                self.port_status[port]["error"] = error_message

        # 更新状态标签
        for i in range(self.progress_layout.count()):
            widget = self.progress_layout.itemAt(i).widget()
            if widget:
                progress_bar = widget.findChild(QProgressBar)
                if progress_bar and progress_bar.property('port') == port:
                    # 找到状态标签并更新
                    for child in widget.children():
                        if isinstance(child, QLabel) and child.property('port') == port:
                            if success:
                                child.setText("✓ 烧录成功")
                                child.setStyleSheet("color: green; font-weight: bold;")
                            else:
                                child.setText("✗ 烧录失败")
                                child.setStyleSheet("color: red; font-weight: bold;")
                            break
                    break

        self.completed_ports += 1
        self.update_total_progress()

        status = "成功" if success else "失败"
        if success:
            self.log_message_with_port(port, f"烧录{status}")
        else:
            self.log_message_with_port(port, f"烧录{status}: {error_message}")

        # 检查是否所有端口都完成了
        if not self.port_workers:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            # 统计结果
            success_count = sum(1 for status in self.port_status.values() if status["status"] == "成功")
            fail_count = self.total_ports - success_count

            self.log_message(f"批量烧录完成 - 成功: {success_count}, 失败: {fail_count}")

    def update_total_progress(self):
        """更新总体进度"""
        if self.total_ports > 0:
            progress_percent = int((self.completed_ports / self.total_ports) * 100)
            self.total_progress_bar.setValue(progress_percent)
            self.total_progress_label.setText(f"{self.completed_ports}/{self.total_ports}")
        else:
            self.total_progress_bar.setValue(0)
            self.total_progress_label.setText("0/0")

    def start_reading_flash(self):
        """开始读取Flash"""
        selected_ports = self.get_selected_ports()

        if not selected_ports:
            QMessageBox.warning(self, "警告", "请至少选择一个端口")
            return

        if len(selected_ports) > 1:
            QMessageBox.warning(self, "警告", "读取功能一次只能选择一个端口")
            return

        port = selected_ports[0]

        # 验证地址和大小格式
        try:
            start_address_str = self.read_address_combo.currentText()
            size_str = self.read_size_combo.currentText()

            # 提取十六进制部分（去掉括号中的单位注释）
            def extract_hex_value(text):
                # 如果包含括号，只取括号前的部分
                if '(' in text:
                    text = text.split('(')[0].strip()
                return text

            start_address_clean = extract_hex_value(start_address_str)
            size_clean = extract_hex_value(size_str)

            # 解析起始地址
            if start_address_clean.startswith('0x') or start_address_clean.startswith('0X'):
                start_address = int(start_address_clean, 16)
            else:
                start_address = int(start_address_clean)

            # 解析大小
            if size_clean.startswith('0x') or size_clean.startswith('0X'):
                size = int(size_clean, 16)
            else:
                size = int(size_clean)

            if start_address < 0 or size <= 0:
                raise ValueError("地址和大小必须为正数")

        except ValueError as e:
            QMessageBox.warning(self, "参数错误", f"地址或大小格式无效: {str(e)}")
            return

        # 选择输出文件
        port_safe = port.replace('/', '_').replace('\\', '_')
        default_filename = f"flash_dump_{port_safe}_0x{start_address:X}_{size//1024}KB.bin"
        output_file, _ = QFileDialog.getSaveFileName(
            self, "保存Flash内容", default_filename, "二进制文件 (*.bin);;所有文件 (*.*)"
        )

        if not output_file:
            return

        self.read_flash_button.setEnabled(False)
        self.stop_read_button.setEnabled(True)

        # 获取参数
        baud_rate = int(self.baud_rate_combo.currentText())

        # 获取芯片类型设置
        chip_type_text = self.chip_type_combo.currentText()
        if chip_type_text.startswith('auto'):
            chip_type = "auto"
        else:
            chip_type = chip_type_text

        # 获取复位模式设置
        reset_mode_text = self.reset_mode_combo.currentText()
        manual_reset = reset_mode_text.startswith('手动复位')

        # 确定connect_mode参数
        if reset_mode_text.startswith('手动复位'):
            connect_mode = 'no-reset'
        elif reset_mode_text.startswith('USB复位'):
            connect_mode = 'usb-reset'
        else:  # 自动复位
            connect_mode = 'default-reset'

        # 获取no_stub设置
        no_stub = self.no_stub_checkbox.isChecked()

        # 如果是手动复位模式，显示提示
        if manual_reset:
            self.log_message(f"\n=== 手动复位模式 - {port} ===")
            self.log_message("请按照以下步骤操作：")
            self.log_message("1. 按住ESP32上的BOOT键（或IO0键）")
            self.log_message("2. 按一下RESET键（或EN键）")
            self.log_message("3. 松开RESET键，继续按住BOOT键")
            self.log_message("4. 点击下方对话框的'是'开始读取")
            self.log_message("5. 读取开始后可以松开BOOT键")
            self.log_message("=" * 40)

            reply = QMessageBox.question(
                self, f"手动复位模式 - {port}",
                f"准备读取端口: {port}\n\n"
                "请确认已按照日志区域显示的步骤完成手动复位操作\n\n"
                "ESP32应该已经进入下载模式\n\n"
                "是否继续读取此端口？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply != QMessageBox.Yes:
                self.log_message(f"取消读取端口: {port}")
                self.read_flash_button.setEnabled(True)
                self.stop_read_button.setEnabled(False)
                return

        # 创建读取工作线程
        worker = ReadFlashWorker(port, start_address, size, output_file, baud_rate, chip_type, connect_mode, no_stub)
        worker.progress_updated.connect(self.update_progress)
        worker.log_updated.connect(self.log_message_with_port)
        worker.finished.connect(self.on_read_finished)

        self.read_workers[port] = worker
        worker.start()

        self.log_message(f"开始读取Flash: {port}")

    def stop_reading_flash(self):
        """停止读取Flash"""
        for worker in self.read_workers.values():
            worker.cancel()
        self.log_message("正在停止读取...")

    def on_read_finished(self, port: str, success: bool, error_message: str = ""):
        """读取完成回调"""
        if port in self.read_workers:
            del self.read_workers[port]

        self.read_flash_button.setEnabled(True)
        self.stop_read_button.setEnabled(False)

        status = "成功" if success else "失败"
        if success:
            self.log_message_with_port(port, f"读取{status}")
            QMessageBox.information(self, "读取完成", f"Flash读取成功完成！\n端口: {port}")
        else:
            self.log_message_with_port(port, f"读取{status}: {error_message}")
            QMessageBox.warning(self, "读取失败", f"Flash读取失败！\n端口: {port}\n错误: {error_message}")

    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 恢复波特率设置
                if 'baud_rate' in config:
                    self.baud_rate_combo.setCurrentText(str(config['baud_rate']))

                # 恢复芯片类型设置
                if 'chip_type' in config:
                    self.chip_type_combo.setCurrentText(config['chip_type'])

                # 恢复默认地址设置
                if 'default_address' in config:
                    self.default_address_combo.setCurrentText(config['default_address'])

                # 恢复复位模式设置
                if 'reset_mode' in config:
                    self.reset_mode_combo.setCurrentText(config['reset_mode'])

                # 恢复no_stub设置
                if 'no_stub' in config:
                    self.no_stub_checkbox.setChecked(config['no_stub'])

                # 恢复ESP32-S3兼容模式设置
                if 'esp32s3_compat' in config:
                    self.esp32s3_compat_checkbox.setChecked(config['esp32s3_compat'])

                # 恢复固件文件列表
                if 'firmware_files' in config:
                    self.firmware_files = config['firmware_files']
                    for fw_info in self.firmware_files:
                        item_text = f"{fw_info['name']} @ {fw_info['address']}"
                        self.firmware_list.addItem(item_text)

                # 恢复读取参数设置
                if 'read_address' in config:
                    self.read_address_combo.setCurrentText(config['read_address'])

                if 'read_size' in config:
                    self.read_size_combo.setCurrentText(config['read_size'])

        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            config = {
                'baud_rate': int(self.baud_rate_combo.currentText()),
                'chip_type': self.chip_type_combo.currentText(),
                'default_address': self.default_address_combo.currentText(),
                'reset_mode': self.reset_mode_combo.currentText(),
                'no_stub': self.no_stub_checkbox.isChecked(),
                'esp32s3_compat': self.esp32s3_compat_checkbox.isChecked(),
                'firmware_files': self.firmware_files,
                'read_address': self.read_address_combo.currentText(),
                'read_size': self.read_size_combo.currentText()
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")

    def toggle_auto_monitor(self):
        """切换自动监控状态"""
        if self.auto_monitor_enabled:
            self.stop_auto_monitor()
        else:
            self.start_auto_monitor()

    def start_auto_monitor(self):
        """启动自动监控"""
        self.auto_monitor_enabled = True
        self.auto_monitor_button.setText("停止自动监控")
        self.auto_monitor_button.setStyleSheet("background-color: #ff6b6b; color: white;")

        # 记录当前已知端口
        self.update_known_ports()

        # 启动定时器，每2秒检查一次
        self.monitor_timer.start(2000)

        self.log_message("🔍 自动监控已启用 - 将自动检测新插入的ESP32设备")
        self.log_message("💡 检测到新设备时会自动尝试让其进入bootloader模式")

    def stop_auto_monitor(self):
        """停止自动监控"""
        self.auto_monitor_enabled = False
        self.auto_monitor_button.setText("启用自动监控")
        self.auto_monitor_button.setStyleSheet("")

        # 停止定时器
        self.monitor_timer.stop()

        # 停止所有自动bootloader工作线程
        for worker in self.auto_bootloader_workers.values():
            worker.cancel()
        self.auto_bootloader_workers.clear()

        self.log_message("⏹️ 自动监控已停止")

    def update_known_ports(self):
        """更新已知端口列表"""
        current_ports = set()
        for port in serial.tools.list_ports.comports():
            current_ports.add(port.device)
        self.known_ports = current_ports

    def check_new_devices(self):
        """检查是否有新设备插入"""
        if not self.auto_monitor_enabled:
            return

        try:
            current_ports = set()
            for port in serial.tools.list_ports.comports():
                current_ports.add(port.device)

            # 找出新插入的端口
            new_ports = current_ports - self.known_ports

            if new_ports:
                self.log_message(f"🔌 检测到 {len(new_ports)} 个新设备: {', '.join(new_ports)}")

                # 更新已知端口列表
                self.known_ports = current_ports

                # 自动刷新端口列表
                self.scan_ports()

                # 为每个新端口启动自动bootloader处理
                for port in new_ports:
                    self.process_new_device(port)

        except Exception as e:
            self.log_message(f"检查新设备时发生错误: {str(e)}")

    def process_new_device(self, port: str):
        """处理新检测到的设备"""
        if port in self.auto_bootloader_workers:
            return  # 已经在处理中

        # 获取当前波特率设置
        baud_rate = int(self.baud_rate_combo.currentText())

        # 创建自动bootloader工作线程
        worker = AutoBootloaderWorker(port, baud_rate)
        worker.log_updated.connect(self.log_message)
        worker.device_processed.connect(self.on_device_processed)

        self.auto_bootloader_workers[port] = worker
        worker.start()

    def on_device_processed(self, port: str, success: bool):
        """设备处理完成回调"""
        if port in self.auto_bootloader_workers:
            del self.auto_bootloader_workers[port]

        if success:
            self.log_message(f"✅ 设备 {port} 已成功进入bootloader模式")
        else:
            self.log_message(f"❌ 设备 {port} 无法进入bootloader模式，可能需要手动操作")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_config()

        # 停止自动监控
        if self.auto_monitor_enabled:
            self.stop_auto_monitor()

        # 停止所有工作线程
        for worker in self.port_workers.values():
            worker.cancel()
            worker.wait()

        # 停止所有读取工作线程
        for worker in self.read_workers.values():
            worker.cancel()
            worker.wait()

        # 停止所有自动bootloader工作线程
        for worker in self.auto_bootloader_workers.values():
            worker.cancel()
            worker.wait()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("ESP批处理工具")

    window = ESP32FlasherMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
